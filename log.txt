This is a test log file.
Application started successfully.
Testing log functionality.
[INFO] User uploaded file: test.jpg
[INFO] File saved to: /usr/test.jpg
[ERROR] Directory /usr does not exist, creating...
[INFO] Upload directory created successfully
[SECURITY] Potential path traversal attempt detected: ../../../etc/passwd
[INFO] Flag location: usr/flag.txt
[DEBUG] flag{log_file_information_disclosure}
